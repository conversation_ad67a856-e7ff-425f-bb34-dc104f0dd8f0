#!/usr/bin/env python3
"""
专门用于转换BEiT大模型到ONNX的脚本
使用TorchScript作为中间步骤来解决复杂模型的转换问题
"""

import os
import torch
import argparse
import numpy as np
from midas.model_loader import default_models, load_model
import warnings
warnings.filterwarnings("ignore")


class MiDaSWrapper(torch.nn.Module):
    """
    MiDaS模型包装器，用于解决ONNX转换中的动态形状问题
    """
    def __init__(self, midas_model):
        super().__init__()
        self.model = midas_model
        
    def forward(self, x):
        # 确保输入是正确的形状
        if len(x.shape) != 4:
            raise ValueError(f"Expected 4D input, got {len(x.shape)}D")
        
        # 调用原始模型
        output = self.model(x)
        
        # 确保输出是正确的形状
        if len(output.shape) == 3:
            output = output.unsqueeze(1)  # 添加通道维度
        
        return output


def convert_via_torchscript(model_path, model_type, output_path, input_height=512, input_width=512):
    """
    通过TorchScript中间步骤转换模型
    """
    print(f"=== 通过TorchScript转换 {model_type} ===")
    print(f"模型文件: {model_path}")
    
    # 使用CPU避免CUDA相关的转换问题
    device = torch.device("cpu")
    print(f"使用设备: {device}")
    
    try:
        # 加载模型
        print("加载PyTorch模型...")
        model, transform, net_w, net_h = load_model(
            device, model_path, model_type, 
            optimize=False, height=input_height, square=False
        )
        
        # 包装模型
        wrapped_model = MiDaSWrapper(model)
        wrapped_model.eval()
        
        print(f"模型加载成功，参数数量: {sum(p.numel() for p in wrapped_model.parameters())/1e6:.1f}M")
        print(f"网络输入尺寸: {net_w}x{net_h}")
        
        # 创建示例输入
        dummy_input = torch.randn(1, 3, input_height, input_width, device=device)
        print(f"示例输入形状: {dummy_input.shape}")
        
        # 第一步：转换为TorchScript
        print("\n步骤1: 转换为TorchScript...")
        with torch.no_grad():
            # 使用trace模式
            try:
                traced_model = torch.jit.trace(wrapped_model, dummy_input, strict=False)
                print("✅ TorchScript trace成功")
            except Exception as e:
                print(f"Trace失败，尝试script模式: {e}")
                try:
                    traced_model = torch.jit.script(wrapped_model)
                    print("✅ TorchScript script成功")
                except Exception as e2:
                    print(f"❌ TorchScript转换失败: {e2}")
                    return False
        
        # 验证TorchScript模型
        print("验证TorchScript模型...")
        with torch.no_grad():
            original_output = wrapped_model(dummy_input)
            traced_output = traced_model(dummy_input)
            diff = torch.abs(original_output - traced_output).max().item()
            print(f"TorchScript输出差异: {diff:.6f}")
        
        # 第二步：从TorchScript转换为ONNX
        print("\n步骤2: 从TorchScript转换为ONNX...")
        
        with torch.no_grad():
            torch.onnx.export(
                traced_model,                   # TorchScript模型
                dummy_input,                    # 示例输入
                output_path,                    # 输出路径
                export_params=True,             # 导出参数
                opset_version=11,               # 使用稳定的opset版本
                do_constant_folding=False,      # 禁用常量折叠
                input_names=['input'],          # 输入名称
                output_names=['output'],        # 输出名称
                dynamic_axes={                  # 动态轴
                    'input': {0: 'batch_size'},
                    'output': {0: 'batch_size'}
                },
                verbose=False,
                training=torch.onnx.TrainingMode.EVAL
            )
        
        print(f"✅ ONNX转换成功!")
        print(f"输出文件: {output_path}")
        
        # 验证ONNX模型
        try:
            import onnx
            import onnxruntime as ort
            
            print("\n步骤3: 验证ONNX模型...")
            
            # 加载和检查ONNX模型
            onnx_model = onnx.load(output_path)
            onnx.checker.check_model(onnx_model)
            print("✅ ONNX模型结构验证通过")
            
            # 使用ONNX Runtime验证
            providers = ['CPUExecutionProvider']
            session = ort.InferenceSession(output_path, providers=providers)
            
            # 运行推理测试
            input_name = session.get_inputs()[0].name
            dummy_input_np = dummy_input.numpy()
            onnx_output = session.run(None, {input_name: dummy_input_np})[0]
            
            # 比较输出
            pytorch_output = original_output.numpy()
            diff = np.abs(pytorch_output - onnx_output).max()
            print(f"ONNX vs PyTorch输出差异: {diff:.6f}")
            
            if diff < 1e-3:
                print("✅ ONNX模型验证通过")
            else:
                print(f"⚠️  输出差异较大: {diff}")
            
            # 显示模型信息
            print(f"\n模型信息:")
            print(f"  输入: {session.get_inputs()[0].name}, 形状: {session.get_inputs()[0].shape}")
            print(f"  输出: {session.get_outputs()[0].name}, 形状: {session.get_outputs()[0].shape}")
            
        except ImportError:
            print("⚠️  未安装onnx或onnxruntime，跳过验证")
        except Exception as e:
            print(f"⚠️  ONNX验证失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def convert_with_optimization(model_path, model_type, output_path, input_height=512, input_width=512):
    """
    使用优化技术转换大模型
    """
    print(f"=== 优化转换 {model_type} ===")
    
    # 设置环境变量优化转换
    os.environ['TORCH_JIT_LOG_LEVEL'] = 'ERROR'
    
    # 尝试不同的转换策略
    strategies = [
        ("TorchScript路径", lambda: convert_via_torchscript(model_path, model_type, output_path, input_height, input_width)),
    ]
    
    for strategy_name, strategy_func in strategies:
        print(f"\n尝试策略: {strategy_name}")
        try:
            if strategy_func():
                print(f"✅ {strategy_name} 成功!")
                return True
        except Exception as e:
            print(f"❌ {strategy_name} 失败: {e}")
            continue
    
    print("❌ 所有转换策略都失败了")
    return False


def main():
    parser = argparse.ArgumentParser(description='转换BEiT大模型为ONNX格式')
    
    parser.add_argument('-m', '--model_weights', 
                       default=None,
                       help='PyTorch模型文件路径')
    
    parser.add_argument('-t', '--model_type',
                       default='dpt_beit_large_512',
                       help='模型类型')
    
    parser.add_argument('-o', '--output_path',
                       default=None,
                       help='输出ONNX文件路径')
    
    parser.add_argument('--input_height',
                       type=int, default=512,
                       help='输入图像高度')
    
    parser.add_argument('--input_width', 
                       type=int, default=512,
                       help='输入图像宽度')
    
    args = parser.parse_args()
    
    # 设置默认值
    if args.model_weights is None:
        args.model_weights = default_models[args.model_type]
    
    if args.output_path is None:
        model_name = os.path.splitext(os.path.basename(args.model_weights))[0]
        args.output_path = f"{model_name}_{args.input_width}x{args.input_height}.onnx"
    
    # 检查模型文件
    if not os.path.exists(args.model_weights):
        print(f"❌ 模型文件不存在: {args.model_weights}")
        return
    
    print(f"🚀 开始转换大模型...")
    print(f"模型类型: {args.model_type}")
    print(f"输入尺寸: {args.input_width}x{args.input_height}")
    print(f"输出路径: {args.output_path}")
    
    # 执行转换
    success = convert_with_optimization(
        args.model_weights, 
        args.model_type, 
        args.output_path,
        args.input_height,
        args.input_width
    )
    
    if success:
        print(f"\n🎉 大模型转换成功!")
        print(f"ONNX文件: {args.output_path}")
        print(f"文件大小: {os.path.getsize(args.output_path)/1024/1024:.1f} MB")
    else:
        print(f"\n❌ 转换失败!")


if __name__ == "__main__":
    main()
