# MiDaS on Android smartphone by using TensorFlow-lite (TFLite)


* Either use Android Studio for compilation.

* Or use ready to install apk-file:
    * Or use URL: https://i.diawi.com/CVb8a9
    * Or use QR-code: 
   
Scan QR-code or open URL -> Press `Install application` -> Press `Download` and wait for download -> Open -> Install -> Open -> Press: Allow MiDaS to take photo and video from the camera While using the APP

![CVb8a9](https://user-images.githubusercontent.com/4096485/97727213-38552500-1ae1-11eb-8b76-4ea11216f76d.png)

----

To use another model, you should convert it to `model_opt.tflite` and place it to the directory: `models\src\main\assets`


----

Original repository: https://github.com/isl-org/MiDaS
