apply plugin: 'com.android.library'
apply plugin: 'de.undercouch.download'

android {
    compileSdkVersion 28
    buildToolsVersion "28.0.0"

    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 28
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    aaptOptions {
    noCompress "tflite"
  }

    lintOptions {
        checkReleaseBuilds false
        // Or, if you prefer, you can continue to check for errors in release builds,
        // but continue the build even when errors are found:
        abortOnError false
    }
}

// Download default models; if you wish to use your own models then
// place them in the "assets" directory and comment out this line.
project.ext.ASSET_DIR = projectDir.toString() + '/src/main/assets'
apply from:'download.gradle'
