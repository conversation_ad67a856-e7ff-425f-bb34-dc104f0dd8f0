apply plugin: 'com.android.application'

android {
    compileSdkVersion 28
    defaultConfig {
        applicationId "org.tensorflow.lite.examples.classification"
        minSdkVersion 21
        targetSdkVersion 28
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    aaptOptions {
        noCompress "tflite"
    }
    compileOptions {
        sourceCompatibility = '1.8'
        targetCompatibility = '1.8'
    }
    lintOptions {
        abortOnError false
    }
    flavorDimensions "tfliteInference"
    productFlavors {
       // The TFLite inference is built using the TFLite Support library.
       support {
           dimension "tfliteInference"
       }
       // The TFLite inference is built using the TFLite Task library.
       taskApi {
           dimension "tfliteInference"
       }
   }

}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    supportImplementation project(":lib_support")
    taskApiImplementation project(":lib_task_api")
    implementation 'androidx.appcompat:appcompat:1.0.0'
    implementation 'androidx.coordinatorlayout:coordinatorlayout:1.0.0'
    implementation 'com.google.android.material:material:1.0.0'

    androidTestImplementation 'androidx.test.ext:junit:1.1.1'
    androidTestImplementation 'com.google.truth:truth:1.0.1'
    androidTestImplementation 'androidx.test:runner:1.2.0'
    androidTestImplementation 'androidx.test:rules:1.1.0'
}
