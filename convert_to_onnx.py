#!/usr/bin/env python3
"""
将MiDaS PyTorch模型转换为ONNX格式
支持所有MiDaS模型类型，包括最新的DPT和BEiT模型
"""

import os
import torch
import argparse
import numpy as np
from midas.model_loader import default_models, load_model


def convert_to_onnx(model_path, model_type, output_path=None, input_height=None, input_width=None,
                   opset_version=11, dynamic_axes=False):
    """
    将MiDaS模型转换为ONNX格式

    Args:
        model_path (str): PyTorch模型文件路径
        model_type (str): 模型类型
        output_path (str): 输出ONNX文件路径，如果为None则自动生成
        input_height (int): 输入图像高度，如果为None则使用模型默认值
        input_width (int): 输入图像宽度，如果为None则使用模型默认值
        opset_version (int): ONNX opset版本
        dynamic_axes (bool): 是否使用动态轴（支持不同输入尺寸）
    """

    print(f"开始转换模型: {model_type}")
    print(f"模型文件: {model_path}")

    # 设置设备 - 对于复杂模型使用CPU可能更稳定
    device = torch.device("cpu")  # 强制使用CPU进行转换
    print(f"使用设备: {device}")

    # 加载模型
    try:
        model, transform, net_w, net_h = load_model(device, model_path, model_type,
                                                   optimize=False, height=input_height, square=False)
        model.eval()
        print(f"模型加载成功，网络输入尺寸: {net_w}x{net_h}")
    except Exception as e:
        print(f"模型加载失败: {e}")
        return False
    
    # 设置输入尺寸
    if input_width is None:
        input_width = net_w
    if input_height is None:
        input_height = net_h
        
    print(f"转换输入尺寸: {input_width}x{input_height}")
    
    # 创建示例输入
    dummy_input = torch.randn(1, 3, input_height, input_width, device=device)
    
    # 设置输出路径
    if output_path is None:
        model_name = os.path.splitext(os.path.basename(model_path))[0]
        output_path = f"{model_name}_{input_width}x{input_height}.onnx"
    
    # 设置动态轴
    dynamic_axes_dict = None
    if dynamic_axes:
        dynamic_axes_dict = {
            'input': {0: 'batch_size', 2: 'height', 3: 'width'},
            'output': {0: 'batch_size', 2: 'height', 3: 'width'}
        }
    
    print("开始ONNX转换...")
    
    try:
        # 执行转换 - 针对复杂模型的特殊设置
        with torch.no_grad():
            # 设置JIT追踪模式
            torch.jit._get_jit_logging().set_level("ERROR")

            # 对于BEiT等复杂模型，使用更保守的设置
            export_kwargs = {
                "model": model,
                "args": dummy_input,
                "f": output_path,
                "export_params": True,
                "opset_version": opset_version,
                "do_constant_folding": False,  # 对复杂模型禁用常量折叠
                "input_names": ['input'],
                "output_names": ['output'],
                "dynamic_axes": dynamic_axes_dict,
                "verbose": True,  # 启用详细输出以便调试
                "training": torch.onnx.TrainingMode.EVAL,
                "operator_export_type": torch.onnx.OperatorExportTypes.ONNX
            }

            # 对于特定模型类型的特殊处理
            if "beit" in model_type or "swin" in model_type:
                export_kwargs["do_constant_folding"] = False
                export_kwargs["opset_version"] = 11  # 使用更稳定的opset版本
                print("检测到Transformer模型，使用特殊转换设置")

            torch.onnx.export(**export_kwargs)
        
        print(f"✅ ONNX转换成功!")
        print(f"输出文件: {output_path}")
        
        # 验证ONNX模型
        try:
            import onnx
            onnx_model = onnx.load(output_path)
            onnx.checker.check_model(onnx_model)
            print("✅ ONNX模型验证通过")
            
            # 显示模型信息
            print(f"模型输入: {onnx_model.graph.input[0].name}")
            print(f"模型输出: {onnx_model.graph.output[0].name}")
            
        except ImportError:
            print("⚠️  未安装onnx包，跳过模型验证")
        except Exception as e:
            print(f"⚠️  ONNX模型验证失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ ONNX转换失败: {e}")
        return False


def main():
    parser = argparse.ArgumentParser(description='将MiDaS模型转换为ONNX格式')
    
    parser.add_argument('-m', '--model_weights', 
                       default=None,
                       help='PyTorch模型文件路径')
    
    parser.add_argument('-t', '--model_type',
                       default='dpt_beit_large_512',
                       choices=['dpt_beit_large_512', 'dpt_beit_large_384', 'dpt_beit_base_384',
                               'dpt_swin2_large_384', 'dpt_swin2_base_384', 'dpt_swin2_tiny_256',
                               'dpt_swin_large_384', 'dpt_next_vit_large_384', 'dpt_levit_224',
                               'dpt_large_384', 'dpt_hybrid_384', 'midas_v21_384', 'midas_v21_small_256'],
                       help='模型类型')
    
    parser.add_argument('-o', '--output_path',
                       default=None,
                       help='输出ONNX文件路径')
    
    parser.add_argument('--input_height',
                       type=int, default=None,
                       help='输入图像高度')
    
    parser.add_argument('--input_width', 
                       type=int, default=None,
                       help='输入图像宽度')
    
    parser.add_argument('--opset_version',
                       type=int, default=11,
                       help='ONNX opset版本')
    
    parser.add_argument('--dynamic_axes',
                       action='store_true',
                       help='使用动态轴支持不同输入尺寸')
    
    args = parser.parse_args()
    
    # 如果没有指定模型路径，使用默认路径
    if args.model_weights is None:
        args.model_weights = default_models[args.model_type]
    
    # 检查模型文件是否存在
    if not os.path.exists(args.model_weights):
        print(f"❌ 模型文件不存在: {args.model_weights}")
        print("请确保模型文件已下载到weights目录")
        return
    
    # 执行转换
    success = convert_to_onnx(
        model_path=args.model_weights,
        model_type=args.model_type,
        output_path=args.output_path,
        input_height=args.input_height,
        input_width=args.input_width,
        opset_version=args.opset_version,
        dynamic_axes=args.dynamic_axes
    )
    
    if success:
        print("\n🎉 转换完成!")
        print("\n使用方法:")
        print("1. 安装ONNX Runtime: pip install onnxruntime")
        print("2. 使用ONNX模型进行推理")
    else:
        print("\n❌ 转换失败!")


if __name__ == "__main__":
    main()
