#!/usr/bin/env python3
"""
使用ONNX模型进行MiDaS深度估计推理
支持图像文件和实时相机输入
"""

import os
import glob
import time
import argparse
import numpy as np
import cv2
try:
    import onnxruntime as ort
except ImportError:
    print("请安装ONNX Runtime: pip install onnxruntime")
    exit(1)

from imutils.video import VideoStream


def preprocess_image(image, target_height, target_width):
    """
    预处理图像以匹配模型输入要求
    
    Args:
        image: 输入图像 (H, W, 3) BGR格式
        target_height: 目标高度
        target_width: 目标宽度
    
    Returns:
        preprocessed_image: 预处理后的图像 (1, 3, H, W)
        original_shape: 原始图像形状
    """
    original_shape = image.shape[:2]
    
    # 转换为RGB
    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # 调整大小
    image_resized = cv2.resize(image_rgb, (target_width, target_height), interpolation=cv2.INTER_CUBIC)
    
    # 归一化到[0,1]
    image_normalized = image_resized.astype(np.float32) / 255.0
    
    # 标准化 (ImageNet标准)
    mean = np.array([0.485, 0.456, 0.406])
    std = np.array([0.229, 0.224, 0.225])
    image_standardized = (image_normalized - mean) / std
    
    # 转换为CHW格式并添加batch维度
    image_chw = np.transpose(image_standardized, (2, 0, 1))
    image_batch = np.expand_dims(image_chw, axis=0)
    
    return image_batch, original_shape


def postprocess_depth(depth_map, original_shape):
    """
    后处理深度图
    
    Args:
        depth_map: 模型输出的深度图
        original_shape: 原始图像形状 (H, W)
    
    Returns:
        processed_depth: 处理后的深度图
    """
    # 移除batch维度
    if len(depth_map.shape) == 4:
        depth_map = depth_map.squeeze(0)
    if len(depth_map.shape) == 3:
        depth_map = depth_map.squeeze(0)
    
    # 调整到原始尺寸
    depth_resized = cv2.resize(depth_map, (original_shape[1], original_shape[0]), interpolation=cv2.INTER_CUBIC)
    
    return depth_resized


def create_depth_visualization(depth_map, colormap=cv2.COLORMAP_INFERNO):
    """
    创建深度图可视化
    
    Args:
        depth_map: 深度图
        colormap: OpenCV颜色映射
    
    Returns:
        colored_depth: 彩色深度图
    """
    # 归一化到0-255
    depth_min = depth_map.min()
    depth_max = depth_map.max()
    depth_normalized = ((depth_map - depth_min) / (depth_max - depth_min) * 255).astype(np.uint8)
    
    # 应用颜色映射
    colored_depth = cv2.applyColorMap(depth_normalized, colormap)
    
    return colored_depth


def run_inference_on_images(onnx_path, input_folder, output_folder):
    """
    对文件夹中的图像进行批量推理
    """
    print(f"加载ONNX模型: {onnx_path}")
    
    # 创建ONNX Runtime会话
    providers = ['CUDAExecutionProvider', 'CPUExecutionProvider']
    session = ort.InferenceSession(onnx_path, providers=providers)
    
    # 获取模型输入输出信息
    input_name = session.get_inputs()[0].name
    input_shape = session.get_inputs()[0].shape
    output_name = session.get_outputs()[0].name
    
    print(f"模型输入: {input_name}, 形状: {input_shape}")
    print(f"模型输出: {output_name}")
    
    # 获取输入尺寸
    target_height = input_shape[2]
    target_width = input_shape[3]
    
    # 获取图像文件列表
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff']
    image_files = []
    for ext in image_extensions:
        image_files.extend(glob.glob(os.path.join(input_folder, ext)))
        image_files.extend(glob.glob(os.path.join(input_folder, ext.upper())))
    
    if not image_files:
        print(f"在 {input_folder} 中未找到图像文件")
        return
    
    print(f"找到 {len(image_files)} 个图像文件")
    
    # 创建输出文件夹
    os.makedirs(output_folder, exist_ok=True)
    
    # 处理每个图像
    for i, image_path in enumerate(image_files):
        print(f"处理 {i+1}/{len(image_files)}: {os.path.basename(image_path)}")
        
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            print(f"无法读取图像: {image_path}")
            continue
        
        # 预处理
        input_tensor, original_shape = preprocess_image(image, target_height, target_width)
        
        # 推理
        start_time = time.time()
        depth_output = session.run([output_name], {input_name: input_tensor})[0]
        inference_time = time.time() - start_time
        
        # 后处理
        depth_map = postprocess_depth(depth_output, original_shape)
        
        # 创建可视化
        depth_colored = create_depth_visualization(depth_map)
        
        # 保存结果
        base_name = os.path.splitext(os.path.basename(image_path))[0]
        depth_path = os.path.join(output_folder, f"{base_name}_depth.png")
        colored_path = os.path.join(output_folder, f"{base_name}_depth_colored.png")
        
        # 保存原始深度图（16位）
        depth_16bit = (depth_map * 1000).astype(np.uint16)  # 转换为毫米
        cv2.imwrite(depth_path, depth_16bit)
        
        # 保存彩色深度图
        cv2.imwrite(colored_path, depth_colored)
        
        print(f"  推理时间: {inference_time:.3f}s")
        print(f"  保存到: {depth_path}")


def run_inference_on_camera(onnx_path, camera_id=0, show_side_by_side=True):
    """
    使用相机进行实时推理
    """
    print(f"加载ONNX模型: {onnx_path}")
    
    # 创建ONNX Runtime会话
    providers = ['CUDAExecutionProvider', 'CPUExecutionProvider']
    session = ort.InferenceSession(onnx_path, providers=providers)
    
    # 获取模型输入输出信息
    input_name = session.get_inputs()[0].name
    input_shape = session.get_inputs()[0].shape
    output_name = session.get_outputs()[0].name
    
    print(f"模型输入: {input_name}, 形状: {input_shape}")
    
    # 获取输入尺寸
    target_height = input_shape[2]
    target_width = input_shape[3]
    
    # 启动相机
    print(f"启动相机 {camera_id}")
    video = VideoStream(camera_id).start()
    time.sleep(2.0)  # 等待相机初始化
    
    fps_counter = 0
    fps_start_time = time.time()
    
    print("开始实时推理，按ESC退出")
    
    try:
        while True:
            # 读取帧
            frame = video.read()
            if frame is None:
                continue
            
            # 预处理
            input_tensor, original_shape = preprocess_image(frame, target_height, target_width)
            
            # 推理
            start_time = time.time()
            depth_output = session.run([output_name], {input_name: input_tensor})[0]
            inference_time = time.time() - start_time
            
            # 后处理
            depth_map = postprocess_depth(depth_output, original_shape)
            depth_colored = create_depth_visualization(depth_map)
            
            # 创建显示图像
            if show_side_by_side:
                display_image = np.concatenate((frame, depth_colored), axis=1)
            else:
                display_image = depth_colored
            
            # 计算FPS
            fps_counter += 1
            if fps_counter % 10 == 0:
                fps = fps_counter / (time.time() - fps_start_time)
                print(f"\rFPS: {fps:.1f}, 推理时间: {inference_time*1000:.1f}ms", end="")
            
            # 显示结果
            cv2.imshow('MiDaS ONNX 深度估计 - 按ESC退出', display_image)
            
            # 检查退出键
            if cv2.waitKey(1) & 0xFF == 27:  # ESC键
                break
                
    except KeyboardInterrupt:
        print("\n用户中断")
    finally:
        video.stop()
        cv2.destroyAllWindows()
        print("\n相机已关闭")


def main():
    parser = argparse.ArgumentParser(description='使用ONNX模型进行MiDaS深度估计')
    
    parser.add_argument('onnx_model', help='ONNX模型文件路径')
    
    parser.add_argument('-i', '--input_path', 
                       default=None,
                       help='输入图像文件夹路径（如果不指定则使用相机）')
    
    parser.add_argument('-o', '--output_path',
                       default='output_onnx',
                       help='输出文件夹路径')
    
    parser.add_argument('-c', '--camera_id',
                       type=int, default=0,
                       help='相机ID')
    
    parser.add_argument('--no_side_by_side',
                       action='store_true',
                       help='相机模式下不显示并排对比')
    
    args = parser.parse_args()
    
    # 检查ONNX模型文件
    if not os.path.exists(args.onnx_model):
        print(f"ONNX模型文件不存在: {args.onnx_model}")
        return
    
    if args.input_path:
        # 图像文件推理模式
        if not os.path.exists(args.input_path):
            print(f"输入文件夹不存在: {args.input_path}")
            return
        run_inference_on_images(args.onnx_model, args.input_path, args.output_path)
    else:
        # 相机实时推理模式
        run_inference_on_camera(args.onnx_model, args.camera_id, not args.no_side_by_side)


if __name__ == "__main__":
    main()
